#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一股票分析CLI工具
兼容股票分析师和量化分析师两种模式，提供完整的股票分析功能

用法：
python scripts/analyze_stocks.py "601869,600522,600487" --mode analyst
python scripts/analyze_stocks.py "601869,600522,600487" --mode quant
python scripts/analyze_stocks.py "TSLA,AAPL" --benchmark "^GSPC" --mode quant
python scripts/analyze_stocks.py --image "screenshot.png" --mode analyst
"""

import sys
import json
import argparse
from stock_data_tool import StockDataTool

# 设置输出编码
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def format_analyst_report(payload: dict) -> str:
    """格式化股票分析师模式的报告"""
    report = []

    # 标题
    report.append("# 📈 股票分析报告")
    report.append(f"**数据截止时间**: {payload.get('as_of', 'Unknown')}")
    report.append("")

    stocks = payload.get('stocks', [])

    # 如果超过3只股票，先给批量摘要
    if len(stocks) > 3:
        report.append("## 📊 批量摘要")
        report.append("| 股票代码 | 股票名称 | 当前价格 | 3月收益 | 技术评分 | 资金评分 | 推荐度 |")
        report.append("|---------|---------|---------|---------|---------|---------|-------|")

        for stock in stocks:
            code = stock.get('symbol', stock.get('code', 'Unknown'))
            name = stock.get('stock_name', 'Unknown')
            price = stock.get('last_close', 0)
            ret_3m = stock.get('cumret_3m', 0) * 100
            tech_score = stock.get('scores', {}).get('technical_score', 0)
            fund_score = stock.get('scores', {}).get('fund_score', 0)

            # 计算推荐度
            total_score = tech_score + fund_score
            if total_score >= 8:
                recommend = "🔥强推"
            elif total_score >= 6:
                recommend = "👍推荐"
            elif total_score >= 4:
                recommend = "⚖️中性"
            else:
                recommend = "⚠️谨慎"

            report.append(f"| {code} | {name} | {price:.2f} | {ret_3m:+.1f}% | {tech_score}⭐ | {fund_score}⭐ | {recommend} |")

        report.append("")

    # 详细分析每只股票
    for i, stock in enumerate(stocks):
        code = stock.get('symbol', stock.get('code', 'Unknown'))
        name = stock.get('stock_name', 'Unknown')
        industry = stock.get('industry', '未知行业')

        report.append(f"## 📊 {name}（{code}）- {industry}")
        report.append("━" * 40)
        report.append("")

        # 1. 三月回顾
        cumret_3m = stock.get('cumret_3m', 0) * 100
        ma_state = stock.get('trend', {}).get('ma_state', 'unknown')
        volume_chg = stock.get('volume', {}).get('chg5_vs_mean', 0)

        report.append("### 1. 三月回顾")
        report.append(f"   • **涨跌幅**: {cumret_3m:+.1f}%")

        # 判断趋势
        if ma_state == 'bullish':
            trend_desc = "多头排列，趋势向上"
        elif ma_state == 'bearish':
            trend_desc = "空头排列，趋势向下"
        else:
            trend_desc = "震荡整理"
        report.append(f"   • **主要趋势**: {trend_desc}")

        # 成交量分析
        if volume_chg > 0.5:
            volume_desc = "显著放量"
        elif volume_chg > 0.2:
            volume_desc = "适度放量"
        elif volume_chg > -0.2:
            volume_desc = "成交量正常"
        else:
            volume_desc = "成交量萎缩"
        report.append(f"   • **成交量**: {volume_desc}")
        report.append("")

        # 2. 技术指标
        rsi14 = stock.get('momentum', {}).get('rsi14', 0)
        bb_pos = stock.get('location', {}).get('bb_pos_0to1', 0.5)
        support_resistance = stock.get('location', {}).get('support_resistance', {})

        report.append("### 2. 技术指标")

        # RSI位置判断
        if rsi14 > 80:
            rsi_desc = f"严重超买({rsi14:.0f})"
        elif rsi14 > 70:
            rsi_desc = f"超买({rsi14:.0f})"
        elif rsi14 < 20:
            rsi_desc = f"严重超卖({rsi14:.0f})"
        elif rsi14 < 30:
            rsi_desc = f"超卖({rsi14:.0f})"
        else:
            rsi_desc = f"正常({rsi14:.0f})"

        # 布林带位置
        if bb_pos > 0.8:
            bb_desc = "接近上轨"
        elif bb_pos > 0.6:
            bb_desc = "上半区"
        elif bb_pos < 0.2:
            bb_desc = "接近下轨"
        elif bb_pos < 0.4:
            bb_desc = "下半区"
        else:
            bb_desc = "中轨附近"

        report.append(f"   • **当前位置**: RSI {rsi_desc}，布林带{bb_desc}")
        report.append(f"   • **均线状态**: {trend_desc}")

        if support_resistance:
            support = support_resistance.get('q10', 0)
            resistance = support_resistance.get('q90', 0)
            report.append(f"   • **关键支撑**: {support:.2f}元")
            report.append(f"   • **关键压力**: {resistance:.2f}元")
        report.append("")

        # 3. 综合评分
        scores = stock.get('scores', {})
        tech_score = scores.get('technical_score', 0)
        fund_score = scores.get('fund_score', 0)

        report.append("### 3. 综合评分")
        report.append(f"   • **技术面**: {'⭐' * tech_score}（{tech_score}/5）")
        report.append(f"   • **资金面**: {'⭐' * fund_score}（{fund_score}/5）")
        report.append(f"   • **消息面**: ⭐⭐⭐⭐⭐（需人工评估）")
        report.append("")

        # 4. 一月预测
        forecast = stock.get('forecast_20d', {})
        target_price = scores.get('target_price', stock.get('last_close', 0))

        report.append("### 4. 一月预测")

        if forecast:
            exp_return = forecast.get('exp', 0) * 100
            range68 = forecast.get('range68', (0, 0))

            # 趋势判断
            if exp_return > 10:
                trend_judge = "强势上涨"
            elif exp_return > 5:
                trend_judge = "温和上涨"
            elif exp_return > -5:
                trend_judge = "震荡整理"
            elif exp_return > -10:
                trend_judge = "温和下跌"
            else:
                trend_judge = "弱势下跌"

            report.append(f"   • **趋势判断**: {trend_judge}")
            report.append(f"   • **目标价位**: {target_price:.2f}元")
            report.append(f"   • **预期收益**: {exp_return:+.1f}%")
            report.append(f"   • **概率区间**: {range68[0]*100:.1f}% ~ {range68[1]*100:.1f}%")

            # 操作建议
            total_score = tech_score + fund_score
            if total_score >= 8 and exp_return > 5:
                operation = "**积极买入**"
            elif total_score >= 6 and exp_return > 0:
                operation = "**适度买入**"
            elif total_score >= 4:
                operation = "**持有观望**"
            else:
                operation = "**谨慎减仓**"

            report.append(f"   • **操作建议**: {operation}")
        else:
            report.append("   • 预测数据暂不可用")
        report.append("")

        # 5. 风险提示
        risk = stock.get('risk', {})
        mdd = risk.get('mdd', 0) * 100
        ann_sigma = risk.get('ann_sigma', 0) * 100

        report.append("### 5. 风险提示")
        report.append(f"   • **主要风险点**: 最大回撤{mdd:.1f}%，年化波动{ann_sigma:.1f}%")

        if support_resistance:
            swing_low = support_resistance.get('swing_low', 0)
            stop_loss = swing_low * 0.95
            report.append(f"   • **止损位建议**: {stop_loss:.2f}元")

        report.append("")
        report.append("━" * 40)
        report.append("")

    # 多股票对比总结
    if len(stocks) > 1:
        report.append("## 🎯 投资建议排序")

        # 按综合评分排序
        sorted_stocks = sorted(stocks,
                             key=lambda x: x.get('scores', {}).get('technical_score', 0) +
                                         x.get('scores', {}).get('fund_score', 0),
                             reverse=True)

        for i, stock in enumerate(sorted_stocks[:3], 1):
            name = stock.get('stock_name', 'Unknown')
            code = stock.get('symbol', stock.get('code', 'Unknown'))
            total_score = (stock.get('scores', {}).get('technical_score', 0) +
                          stock.get('scores', {}).get('fund_score', 0))

            if i == 1:
                report.append(f"🥇 **首选**: {name}({code}) - 综合评分{total_score}/10")
            elif i == 2:
                report.append(f"🥈 **次选**: {name}({code}) - 综合评分{total_score}/10")
            else:
                report.append(f"🥉 **备选**: {name}({code}) - 综合评分{total_score}/10")

        report.append("")

    # 免责声明
    report.append("---")
    report.append("**免责声明**: 分析仅供参考，不构成投资建议。股市有风险，投资需谨慎。")

    return "\n".join(report)


def format_quant_report(payload: dict, result=None) -> str:
    """格式化量化分析师模式的报告"""
    report = []

    # 如果有result对象，使用工具内置的格式化报告
    if result:
        tool = StockDataTool()
        try:
            formatted_report = tool.format_analysis_report(result, mode='quant')
            if formatted_report:
                report.append(formatted_report)
                report.append("")
        except Exception as e:
            print(f"内置报告生成失败: {e}", file=sys.stderr)

    # 添加详细的量化分析
    stocks = payload.get('stocks', [])

    report.append("\n## 📈 详细量化分析")

    for stock in stocks:
        code = stock.get('symbol', stock.get('code', 'Unknown'))
        name = stock.get('stock_name', 'Unknown')

        report.append(f"\n### {name} ({code})")
        report.append("---")

        # 基本统计
        last_close = stock.get('last_close', 0)
        cumret_3m = stock.get('cumret_3m', 0) * 100
        mu_d = stock.get('mu_d', 0) * 100
        sigma_d = stock.get('sigma_d', 0) * 100
        ann_sigma = stock.get('ann_sigma', 0) * 100
        mdd = stock.get('mdd', 0) * 100
        sharpe = stock.get('sharpe', 0)

        report.append("**描述统计（近三个月）**")
        report.append(f"- 当前价格: **{last_close:.2f}元**")
        report.append(f"- 累计收益: **{cumret_3m:+.1f}%**")
        report.append(f"- 日均收益: {mu_d:+.2f}%")
        report.append(f"- 日波动率: {sigma_d:.2f}% (年化: {ann_sigma:.1f}%)")
        report.append(f"- 最大回撤: {mdd:.1f}%")
        report.append(f"- 夏普比率: {sharpe:.2f}")
        report.append("")

        # 技术面分析
        rsi14 = stock.get('rsi14', 0)
        macd = stock.get('macd', 0)
        macd_hist = stock.get('macd_hist', 0)
        bb_pos = stock.get('bb_pos_0to1', 0.5)
        atr14 = stock.get('atr14', 0)

        report.append("**技术面分析**")
        report.append(f"- RSI(14): **{rsi14:.1f}** {'[超买]' if rsi14 > 70 else '[超卖]' if rsi14 < 30 else '[正常]'}")
        report.append(f"- MACD: {macd:.3f}")
        report.append(f"- MACD柱线: **{macd_hist:+.3f}** {'[金叉]' if macd_hist > 0 else '[死叉]'}")
        report.append(f"- 布林带位置: {bb_pos:.2f} {'[上轨]' if bb_pos > 0.8 else '[下轨]' if bb_pos < 0.2 else '[中轨]'}")
        report.append(f"- ATR(14): {atr14:.2f}")
        report.append("")

        # 相对强弱
        relative = stock.get('relative_to_benchmark', {})
        if relative:
            beta = relative.get('beta', 0)
            alpha_ann = relative.get('alpha_ann', 0) * 100
            excess_ret = relative.get('excess_ret_vs_idx', 0) * 100

            report.append("**相对强弱分析**")
            report.append(f"- Beta系数: **{beta:.2f}** {'[高波动]' if beta > 1.2 else '[低波动]' if beta < 0.8 else '[正常]'}")
            report.append(f"- Alpha(年化): **{alpha_ann:+.1f}%**")
            report.append(f"- 超额收益: **{excess_ret:+.1f}%** {'[跑赢大盘]' if excess_ret > 0 else '[跑输大盘]'}")
            report.append("")

        # 预测与情景
        forecast = stock.get('forecast_20d', {})
        if forecast:
            exp_ret = forecast.get('exp', 0) * 100
            range68 = forecast.get('range68', (0, 0))
            range95 = forecast.get('range95', (0, 0))

            report.append("**预测与情景（未来20个交易日）**")
            report.append(f"- 期望收益: **{exp_ret:+.1f}%**")
            report.append(f"- 68%置信区间: **{range68[0]*100:+.1f}% ~ {range68[1]*100:+.1f}%**")
            report.append(f"- 95%置信区间: {range95[0]*100:+.1f}% ~ {range95[1]*100:+.1f}%")

            # 情景分析
            if exp_ret > 10:
                scenario = "乐观情景概率较高"
            elif exp_ret > 0:
                scenario = "基准情景为主"
            else:
                scenario = "保守情景需关注"

            report.append(f"- 情景判断: **{scenario}**")
            report.append("")

        # 关键价位与风控
        support_resistance = stock.get('support_resistance', {})
        if support_resistance:
            support = support_resistance.get('q10', 0)
            resistance = support_resistance.get('q90', 0)
            swing_low = support_resistance.get('swing_low', 0)

            report.append("**关键价位与风控**")
            report.append(f"- 支撑位: **{support:.2f}元**")
            report.append(f"- 阻力位: **{resistance:.2f}元**")
            report.append(f"- 止损建议: **{swing_low * 0.95:.2f}元** (基于ATR)")
            report.append("")

    # 风险声明
    report.append("---")
    report.append("**重要声明**: 本分析基于历史数据与若干假设，存在误差与不确定性；**不构成投资建议**。")

    return "\n".join(report)


def main():
    parser = argparse.ArgumentParser(description='统一股票分析工具')
    parser.add_argument('codes', nargs='?', help='股票代码，逗号分隔，如: 601869,600522,600487')
    parser.add_argument('--mode', choices=['quant', 'analyst', 'comprehensive'], default='analyst',
                       help='分析模式: quant(量化分析师), analyst(股票分析师), comprehensive(综合分析)')
    parser.add_argument('--benchmark', default=None,
                       help='基准指数，默认自动推断(A股:000300.SH, 美股:^GSPC, 港股:^HSI)')
    parser.add_argument('--period', default='9mo',
                       help='数据周期，默认9个月')
    parser.add_argument('--image', help='股票截图路径，用于OCR识别')
    parser.add_argument('--format', choices=['report', 'json', 'summary'], default='report',
                       help='输出格式: report(格式化报告) json(原始数据) summary(简要总结)')

    args = parser.parse_args()

    # 检查输入
    if not args.codes and not args.image:
        print("错误: 请提供股票代码或截图", file=sys.stderr)
        parser.print_help()
        return 1

    try:
        # 自动推断基准
        benchmark = args.benchmark
        if not benchmark and args.codes:
            if any(code.endswith('.HK') or len(code) == 4 for code in args.codes.split(',')):
                benchmark = '^HSI'  # 港股
            elif any(code.isalpha() and '.' not in code for code in args.codes.split(',')):
                benchmark = '^GSPC'  # 美股
            else:
                benchmark = '000300.SH'  # A股默认

        # 初始化工具
        tool = StockDataTool(
            benchmark=benchmark or '000300.SH',
            period=args.period
        )

        # 解析输入
        image_paths = [args.image] if args.image else []
        codes, ocr_info = tool.enhanced_parse_inputs(
            codes_text=args.codes,
            image_paths=image_paths
        )

        if not codes:
            print("错误: 未能解析出有效的股票代码", file=sys.stderr)
            return 1

        print(f"解析到股票代码: {codes}", file=sys.stderr)

        # 获取数据
        result = tool.fetch(codes)

        if result.errors:
            print(f"数据获取错误: {result.errors}", file=sys.stderr)

        # 数据验证
        if ocr_info and args.image:
            validation = tool.validate_realtime_data(ocr_info, result.metrics)
            if validation['warnings']:
                print(f"数据验证警告: {validation['warnings']}", file=sys.stderr)

        # 生成输出
        payload = tool.to_prompt_payload(result, mode=args.mode)

        if args.format == 'json':
            print(json.dumps(payload, ensure_ascii=False, indent=2))
        elif args.format == 'summary':
            # 简要总结格式
            print(f"数据覆盖期: {payload.get('as_of', 'Unknown')}")
            print(f"成功获取 {len(payload.get('stocks', []))} 只股票数据")
            for stock in payload.get('stocks', []):
                name = stock.get('stock_name', stock.get('code', 'Unknown'))
                price = stock.get('last_close', 0)
                ret_3m = stock.get('cumret_3m', 0) * 100
                print(f"- {name}: {price:.2f}元 (3月涨跌: {ret_3m:+.1f}%)")
        else:
            # 格式化报告
            if args.mode == 'analyst':
                formatted_report = format_analyst_report(payload)
            elif args.mode == 'comprehensive':
                # 使用工具内置的comprehensive格式
                formatted_report = tool.format_analysis_report(result, mode='comprehensive')
            else:
                formatted_report = format_quant_report(payload, result)

            print(formatted_report)

        return 0

    except Exception as e:
        print(f"执行错误: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return 1

if __name__ == "__main__":
    sys.exit(main())
